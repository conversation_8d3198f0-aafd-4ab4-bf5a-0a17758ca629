# Resource Coverage Calculation Example

This document demonstrates how to use the new resource coverage calculation functionality in the inspection monitoring service.

## Overview

The enhanced `InspectionTaskMonitoringService` now provides:

1. **Resource Information Tracking**: Host-level execution results now include resource ID and name
2. **Resource Coverage Rate Calculation**: Automatic calculation of what percentage of resources are successfully covered by inspections
3. **Resource Execution Statistics**: Detailed statistics for each resource's success/failure counts

## Key Features

### 1. Enhanced Rule Execution Recording

```java
// New method with resource information
monitoringService.recordRuleExecutionResult(
    taskId, 
    ruleId, 
    pluginId, 
    result, 
    resourceId,     // NEW: Resource ID
    resourceName    // NEW: Resource Name
);
```

### 2. Resource Coverage Rate Calculation

```java
// Calculate coverage rate for a task
Integer coverageRate = monitoringService.calculateResourceCoverageRate(taskId);
// Returns percentage (0-100) of resources with successful executions
```

### 3. Resource Execution Statistics

```java
// Get detailed statistics for each resource
Map<String, ResourceExecutionStats> stats = 
    monitoringService.getResourceExecutionStatistics(taskId);

for (ResourceExecutionStats resourceStats : stats.values()) {
    System.out.println("Resource: " + resourceStats.getResourceId());
    System.out.println("Success Count: " + resourceStats.getSuccessCount());
    System.out.println("Fail Count: " + resourceStats.getFailCount());
    System.out.println("Success Rate: " + resourceStats.getSuccessRate() + "%");
    System.out.println("Is Healthy: " + resourceStats.isHealthy());
}
```

## Implementation Details

### Database Changes

The `TaskRuleExecutionStatusDO` entity now properly utilizes existing fields:
- `resourceId`: Stores the resource identifier
- `resourceName`: Stores the resource display name

### Coverage Rate Calculation Logic

1. **Query all resources** associated with the task from `inspection_task_rule_resource` table
2. **Analyze execution records** from `inspection_task_rule_execution_status` table
3. **Count successful resources**: Resources that have at least one successful execution
4. **Calculate percentage**: `(successful_resources / total_resources) * 100`

### Example Scenario

Given a task with 3 resources:
- Resource A: 2 successful executions, 1 failed execution → **Covered**
- Resource B: 0 successful executions, 2 failed executions → **Not Covered**
- Resource C: 1 successful execution, 0 failed executions → **Covered**

**Coverage Rate**: 2/3 = 66%

## Integration Points

### AsyncTaskExecutionServiceImpl

The service now automatically passes resource information when recording execution results:

```java
// Before
monitoringService.recordRuleExecutionResult(taskId, ruleId, pluginId, result);

// After
monitoringService.recordRuleExecutionResult(taskId, ruleId, pluginId, result, 
    resource.getResourceId(), getResourceName(resource));
```

### Task Completion

When a task completes, the coverage rate is automatically calculated and stored:

```java
// In markTaskAsCompleted method
Integer coverageRate = calculateResourceCoverageRate(taskId);
if (coverageRate != null) {
    executionStatus.setCoverageRate(coverageRate);
}
```

## Benefits

1. **Better Visibility**: Track which resources are being successfully monitored
2. **Health Monitoring**: Identify resources that consistently fail inspections
3. **Coverage Metrics**: Understand inspection effectiveness across your infrastructure
4. **Troubleshooting**: Quickly identify problematic resources or patterns

## Future Enhancements

1. **Resource Name Resolution**: Implement proper resource name lookup from resource management service
2. **Trend Analysis**: Track coverage rate changes over time
3. **Alerting**: Set up alerts for low coverage rates or consistently failing resources
4. **Dashboard Integration**: Display coverage metrics in monitoring dashboards
