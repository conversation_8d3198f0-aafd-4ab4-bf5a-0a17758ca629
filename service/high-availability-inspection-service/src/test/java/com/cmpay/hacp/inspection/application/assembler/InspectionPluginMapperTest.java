package com.cmpay.hacp.inspection.application.assembler;

import com.cmpay.hacp.inspection.domain.model.enums.*;
import com.cmpay.hacp.inspection.domain.model.plugin.InspectionPlugin;
import com.cmpay.hacp.inspection.domain.model.plugin.PluginScriptParameter;
import com.cmpay.hacp.inspection.domain.model.plugin.PluginScriptResult;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PluginDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PluginScriptDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PluginScriptParameterDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PluginScriptOutputFieldDO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 巡检插件对象转换器单元测试
 */
class InspectionPluginMapperTest {

    private InspectionPluginMapper mapper;
    private InspectionPlugin inspectionPlugin;
    private final String PLUGIN_ID = "PLUGIN-000001";

    @BeforeEach
    void setUp() {
        // 初始化mapper
        mapper = Mappers.getMapper(InspectionPluginMapper.class);

        // 创建测试数据
        inspectionPlugin = new InspectionPlugin();
        inspectionPlugin.setPluginId(PLUGIN_ID);
        inspectionPlugin.setName("CPU使用率检查");
        inspectionPlugin.setType(PluginType.SHELL_SCRIPT);
        inspectionPlugin.setStatus(PluginStatus.ENABLE);
        inspectionPlugin.setDescription("检查服务器CPU使用率是否超过阈值");
        inspectionPlugin.setScriptContent("#!/bin/bash\necho \"CPU使用率检查\"");
        inspectionPlugin.setScriptResultType(ScriptResultType.STRUCTURED);
        inspectionPlugin.setTagIds(Arrays.asList(1L, 2L, 3L));

        // 设置输出字段定义
        List<PluginScriptResult> results = new ArrayList<>();
        PluginScriptResult result = new PluginScriptResult();
        result.setFieldName("cpu.usage");
        result.setExampleValue("85.5");
        result.setFieldUnit("%");
        result.setFieldType(ScriptResultFieldType.NUMERIC);
        result.setDescription("CPU使用率");
        results.add(result);
        inspectionPlugin.setResults(results);

        // 设置参数设置
        List<PluginScriptParameter> parameters = new ArrayList<>();
        PluginScriptParameter parameter = new PluginScriptParameter();
        parameter.setParamName("threshold");
        parameter.setParamType(ParamType.TEXT);
        parameter.setRegexPattern("^[0-9]+$");
        parameter.setParamValue("90");
        parameter.setParamDesc("告警阈值百分比");
        parameter.setIsEncrypted(false);
        parameters.add(parameter);
        inspectionPlugin.setParameters(parameters);
    }

    @Test
    @DisplayName("测试领域对象转插件DO")
    void testToInspectionPluginDO() {
        // 执行转换
        PluginDO result = mapper.toInspectionPluginDO(inspectionPlugin);

        // 验证基本字段映射
        assertNotNull(result);
        // pluginId应该被忽略，因为有@Mapping(target = "pluginId", ignore = true)
        assertNull(result.getPluginId());
        assertEquals(inspectionPlugin.getName(), result.getName());
        assertEquals(inspectionPlugin.getDescription(), result.getDescription());

        // 验证枚举类型转换
        assertEquals(PluginType.SHELL_SCRIPT, result.getType());
        assertEquals(PluginStatus.ENABLE, result.getStatus());
    }

    @Test
    @DisplayName("测试领域对象转脚本DO")
    void testToPluginScriptDO() {
        // 执行转换
        PluginScriptDO result = mapper.toPluginScriptDO(inspectionPlugin);

        // 验证基本字段映射
        assertNotNull(result);
        assertEquals(inspectionPlugin.getPluginId(), result.getPluginId());
        assertEquals(inspectionPlugin.getScriptContent(), result.getScriptContent());

        // 验证枚举类型转换
        assertEquals(ScriptResultType.STRUCTURED, result.getScriptResultType());
    }

    @Test
    @DisplayName("测试领域对象转脚本结果DO列表")
    void testToPluginScriptResultDOList() {
        // 执行转换
        List<PluginScriptOutputFieldDO> resultList = inspectionPlugin.getResults().stream()
                .map(scriptResult -> mapper.toPluginScriptResultDO(scriptResult, PLUGIN_ID))
                .collect(Collectors.toList());

        // 验证列表转换
        assertNotNull(resultList);
        assertEquals(1, resultList.size());
        
        // 验证第一个元素
        PluginScriptOutputFieldDO resultDO = resultList.get(0);
        assertEquals(PLUGIN_ID, resultDO.getPluginId());
        assertEquals("cpu.usage", resultDO.getFieldName());
        assertEquals("85.5", resultDO.getExampleValue());
        assertEquals("%", resultDO.getFieldUnit());
        assertEquals(ScriptResultFieldType.NUMERIC, resultDO.getFieldType());
        assertEquals("CPU使用率", resultDO.getDescription());
    }

    @Test
    @DisplayName("测试领域对象转脚本参数DO列表")
    void testToPluginScriptParameterDOList() {
        // 执行转换
        ArrayList<PluginScriptParameterDO> resultList = inspectionPlugin.getParameters().stream()
                .map(scriptParameter -> mapper.toPluginScriptParameterDO(scriptParameter, PLUGIN_ID))
                .collect(Collectors.toCollection(ArrayList::new));

        // 验证列表转换
        assertNotNull(resultList);
        assertEquals(1, resultList.size());
        
        // 验证第一个元素
        PluginScriptParameterDO parameterDO = resultList.get(0);
        assertEquals(PLUGIN_ID, parameterDO.getPluginId());
        assertEquals("threshold", parameterDO.getParamName());
        assertEquals(ParamType.TEXT, parameterDO.getParamType());
        assertEquals("^[0-9]+$", parameterDO.getRegexPattern());
        assertEquals("90", parameterDO.getParamValue());
        assertEquals("告警阈值百分比", parameterDO.getParamDesc());
        assertEquals(false, parameterDO.getIsEncrypted());
    }
}
