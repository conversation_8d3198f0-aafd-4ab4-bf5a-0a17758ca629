package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cmpay.hacp.inspection.domain.model.plugin.InspectionResult;
import com.cmpay.hacp.inspection.domain.model.task.ResourceExecutionStats;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskExecutionDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.RuleExecutionDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskRuleResourceDO;
import com.cmpay.hacp.inspection.infrastructure.repository.TaskExecutionRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.RuleExecutionRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.TaskRuleResourceRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class InspectionTaskMonitoringServiceImplTest {

    @Mock
    private TaskExecutionRepository taskExecutionRepository;

    @Mock
    private RuleExecutionRepository ruleExecutionRepository;

    @Mock
    private TaskRuleResourceRepository taskRuleResourceRepository;

    @InjectMocks
    private InspectionTaskMonitoringServiceImpl monitoringService;

    private TaskExecutionDO mockTaskStatus;
    private String testTaskId = "TASK-000001";

    @BeforeEach
    void setUp() {
        mockTaskStatus = new TaskExecutionDO();
        mockTaskStatus.setId(1L);
        mockTaskStatus.setTaskId(testTaskId);
        mockTaskStatus.setTaskName("Test Task");
        mockTaskStatus.setRuleTotal(3);
        mockTaskStatus.setRuleSuccessCount(0);
        mockTaskStatus.setRuleFailCount(0);
    }

    @Test
    void testRecordRuleExecutionResultWithResourceInfo() {
        // Given
        when(taskExecutionRepository.getOne(any(LambdaQueryWrapper.class)))
                .thenReturn(mockTaskStatus);

        InspectionResult result = InspectionResult.builder()
                .success(true)
                .message("Success")
                .details("Test details")
                .pluginName("TestPlugin")
                .build();

        String resourceId = "RESOURCE-001";
        String resourceName = "Test Resource";

        // When
        monitoringService.recordRuleExecutionResult(testTaskId, "RULE-001", "PLUGIN-001", 
                result, resourceId, resourceName);

        // Then
        verify(ruleExecutionRepository).save(argThat(ruleStatus -> {
            assertEquals("RULE-001", ruleStatus.getRuleId());
            assertEquals("PLUGIN-001", ruleStatus.getPluginId());
            assertEquals("1", ruleStatus.getExecutionStatus());
            assertEquals(resourceId, ruleStatus.getResourceId());
            assertEquals(resourceName, ruleStatus.getResourceName());
            return true;
        }));

        verify(taskExecutionRepository).updateById(argThat(taskStatus -> {
            assertEquals(1, taskStatus.getRuleSuccessCount());
            assertEquals(0, taskStatus.getRuleFailCount());
            return true;
        }));
    }

    @Test
    void testCalculateResourceCoverageRate() {
        // Given
        when(taskExecutionRepository.getOne(any(LambdaQueryWrapper.class)))
                .thenReturn(mockTaskStatus);

        // Mock task resources
        List<TaskRuleResourceDO> taskResources = Arrays.asList(
                createTaskRuleResource("RESOURCE-001"),
                createTaskRuleResource("RESOURCE-002"),
                createTaskRuleResource("RESOURCE-003")
        );
        when(taskRuleResourceRepository.list(any(LambdaQueryWrapper.class)))
                .thenReturn(taskResources);

        // Mock execution records - 2 resources have successful executions
        List<RuleExecutionDO> executionRecords = Arrays.asList(
                createExecutionRecord("RESOURCE-001", "1"), // success
                createExecutionRecord("RESOURCE-001", "0"), // fail
                createExecutionRecord("RESOURCE-002", "1"), // success
                createExecutionRecord("RESOURCE-003", "0")  // fail only
        );
        when(ruleExecutionRepository.list(any(LambdaQueryWrapper.class)))
                .thenReturn(executionRecords);

        // When
        Integer coverageRate = monitoringService.calculateResourceCoverageRate(testTaskId);

        // Then
        assertNotNull(coverageRate);
        assertEquals(66, coverageRate); // 2 out of 3 resources have successful executions = 66%
    }

    @Test
    void testGetResourceExecutionStatistics() {
        // Given
        when(taskExecutionRepository.getOne(any(LambdaQueryWrapper.class)))
                .thenReturn(mockTaskStatus);

        List<RuleExecutionDO> executionRecords = Arrays.asList(
                createExecutionRecord("RESOURCE-001", "1"), // success
                createExecutionRecord("RESOURCE-001", "1"), // success
                createExecutionRecord("RESOURCE-001", "0"), // fail
                createExecutionRecord("RESOURCE-002", "1"), // success
                createExecutionRecord("RESOURCE-002", "0")  // fail
        );
        when(ruleExecutionRepository.list(any(LambdaQueryWrapper.class)))
                .thenReturn(executionRecords);

        // When
        Map<String, ResourceExecutionStats> stats =
                monitoringService.getResourceExecutionStatistics(testTaskId);

        // Then
        assertNotNull(stats);
        assertEquals(2, stats.size());

        ResourceExecutionStats resource1Stats = stats.get("RESOURCE-001");
        assertNotNull(resource1Stats);
        assertEquals(2, resource1Stats.getSuccessCount());
        assertEquals(1, resource1Stats.getFailCount());
        assertEquals(3, resource1Stats.getTotalCount());
        assertEquals(66.67, resource1Stats.getSuccessRate(), 0.01);

        ResourceExecutionStats resource2Stats = stats.get("RESOURCE-002");
        assertNotNull(resource2Stats);
        assertEquals(1, resource2Stats.getSuccessCount());
        assertEquals(1, resource2Stats.getFailCount());
        assertEquals(2, resource2Stats.getTotalCount());
        assertEquals(50.0, resource2Stats.getSuccessRate(), 0.01);
    }

    private TaskRuleResourceDO createTaskRuleResource(String resourceId) {
        TaskRuleResourceDO resource = new TaskRuleResourceDO();
        resource.setTaskId(testTaskId);
        resource.setRuleId("RULE-001");
        resource.setResourceId(resourceId);
        return resource;
    }

    private RuleExecutionDO createExecutionRecord(String resourceId, String status) {
        RuleExecutionDO record = new RuleExecutionDO();
        record.setTaskExecutionId(1L);
        record.setRuleId("RULE-001");
        record.setPluginId("PLUGIN-001");
        record.setResourceId(resourceId);
        record.setResourceName("Resource " + resourceId);
        record.setExecutionStatus(status);
        return record;
    }
}
