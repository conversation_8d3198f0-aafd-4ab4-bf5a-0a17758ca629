package com.cmpay.hacp.inspection.infrastructure.repository;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskExecutionDO;
import com.cmpay.hacp.inspection.infrastructure.mapper.TaskExecutionMapper;
import org.springframework.stereotype.Repository;

/**
 * 任务执行状态Repository
 */
@Repository
public class TaskExecutionRepository extends CrudRepository<TaskExecutionMapper, TaskExecutionDO> {
}
