package com.cmpay.hacp.inspection.application.assembler;

import com.cmpay.hacp.inspection.domain.model.task.InspectionTask;
import com.cmpay.hacp.inspection.domain.model.task.TaskRuleExecution;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreAuditFields;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskRuleDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface InspectionTaskMapper {
    @IgnoreAuditFields
    TaskDO toInspectionTaskDO(InspectionTask inspectionTask);

    InspectionTask toInspectionTask(TaskDO taskDO);

//    @Mapping(target = "taskRuleExecutions", ignore = true)
//    @Mapping(target = "auditInfo.createdBy", source = "inspectionTaskDO.createdByName")
//    @Mapping(target = "auditInfo.createdTime", source = "inspectionTaskDO.createdTime")
//    @Mapping(target = "auditInfo.updatedBy", source = "inspectionTaskDO.updatedByName")
//    @Mapping(target = "auditInfo.updatedTime", source = "inspectionTaskDO.updatedTime")
//    @Mapping(target = "scheduleConfig.enabled", source = "taskScheduleConfigDO.enabled")
//    InspectionTask toInspectionTask(InspectionTaskDO inspectionTaskDO, TaskScheduleConfigDO taskScheduleConfigDO);

    @Mapping(target = "targetEnvironmentId", ignore = true)
    List<TaskRuleExecution> toTaskRuleExecutions(List<TaskRuleDO> taskRuleDOS);

}
