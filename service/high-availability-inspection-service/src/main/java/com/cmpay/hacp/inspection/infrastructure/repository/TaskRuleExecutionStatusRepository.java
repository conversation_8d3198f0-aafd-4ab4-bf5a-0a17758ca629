package com.cmpay.hacp.inspection.infrastructure.repository;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.InspectionRuleExecutionDO;
import com.cmpay.hacp.inspection.infrastructure.mapper.RuleExecutionMapper;
import org.springframework.stereotype.Repository;

/**
 * 规则执行状态Repository
 */
@Repository
public class TaskRuleExecutionStatusRepository extends CrudRepository<RuleExecutionMapper, InspectionRuleExecutionDO> {
}
