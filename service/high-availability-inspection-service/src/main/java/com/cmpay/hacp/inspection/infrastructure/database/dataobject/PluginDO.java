package com.cmpay.hacp.inspection.infrastructure.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cmpay.hacp.inspection.domain.model.enums.PluginStatus;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 巡检插件实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inspection_plugin")
public class PluginDO extends BaseDO {
    /**
     * 插件ID
     */
    private String pluginId;

    /**
     * 插件名称
     */
    private String name;

    /**
     * 插件类型(SHELL、PYTHON等)
     */
    private PluginType type;

    /**
     * 插件状态(0禁用，1启用)
     */
    private PluginStatus status;

    /**
     * 插件描述
     */
    private String description;
}
