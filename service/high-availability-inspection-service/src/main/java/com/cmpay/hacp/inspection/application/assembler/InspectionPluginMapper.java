package com.cmpay.hacp.inspection.application.assembler;

import com.cmpay.hacp.inspection.domain.model.plugin.InspectionPlugin;
import com.cmpay.hacp.inspection.domain.model.plugin.PluginScriptParameter;
import com.cmpay.hacp.inspection.domain.model.plugin.PluginScriptResult;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreAuditFields;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PluginDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PluginScriptDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PluginScriptOutputFieldDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PluginScriptParameterDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 巡检插件对象转换器
 * 用于领域对象和实体对象之间的转换
 */
@Mapper(componentModel = "spring")
public interface InspectionPluginMapper {

    @IgnoreAuditFields
    @Mapping(target = "pluginId", ignore = true)
    PluginDO toInspectionPluginDO(InspectionPlugin inspectionPlugin);

    @IgnoreAuditFields
    PluginScriptDO toPluginScriptDO(InspectionPlugin inspectionPlugin);

    @IgnoreAuditFields
    PluginScriptOutputFieldDO toPluginScriptResultDO(PluginScriptResult scriptResult, String pluginId);

    @IgnoreAuditFields
    PluginScriptParameterDO toPluginScriptParameterDO(PluginScriptParameter scriptParameter, String pluginId);

    @Mapping(target = "tagIds", ignore = true)
    @Mapping(target = "auditInfo.createdBy", source = "pluginDO.createdByName")
    @Mapping(target = "auditInfo.createdTime", source = "pluginDO.createdTime")
    @Mapping(target = "auditInfo.updatedBy", source = "pluginDO.updatedByName")
    @Mapping(target = "auditInfo.updatedTime", source = "pluginDO.updatedTime")
    @Mapping(target = "pluginId", source = "pluginDO.pluginId")
    InspectionPlugin toInspectionPlugin(PluginDO pluginDO, PluginScriptDO scriptDO, List<PluginScriptOutputFieldDO> results, List<PluginScriptParameterDO> parameters);

    InspectionPlugin toInspectionPlugin(PluginDO pluginDO);
}
