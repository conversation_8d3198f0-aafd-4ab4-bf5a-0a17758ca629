package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cmpay.hacp.inspection.application.service.InspectionReportService;
import com.cmpay.hacp.inspection.domain.model.report.DailyInspectionReport;
import com.cmpay.hacp.inspection.domain.model.report.InspectionTaskReport;
import com.cmpay.hacp.inspection.domain.model.report.ResourceInspectionReport;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskExecutionDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.RuleExecutionDO;
import com.cmpay.hacp.inspection.infrastructure.repository.TaskExecutionRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.TaskRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.RuleExecutionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 巡检报告服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InspectionReportServiceImpl implements InspectionReportService {
    private final TaskRepository taskRepository;
    private final TaskExecutionRepository taskExecutionRepository;
    private final RuleExecutionRepository ruleExecutionRepository;
    
    @Override
    public InspectionTaskReport getTaskExecutionReport(String taskId, Long executionId) {
        // 获取任务信息
        TaskDO taskDO = taskRepository.getOne(
                Wrappers.lambdaQuery(TaskDO.class)
                        .eq(TaskDO::getTaskId, taskId));
        
        if (taskDO == null) {
            log.warn("Task not found: {}", taskId);
            return null;
        }
        
        // 获取任务执行状态
        TaskExecutionDO executionStatusDO;
        if (executionId != null) {
            // 获取指定执行ID的执行状态
            executionStatusDO = taskExecutionRepository.getById(executionId);
        } else {
            // 获取最新一次执行的状态
            Long numericTaskId = Long.valueOf(taskId.replaceAll("\\D+", ""));
            executionStatusDO = taskExecutionRepository.getOne(
                    Wrappers.lambdaQuery(TaskExecutionDO.class)
                            .eq(TaskExecutionDO::getTaskId, numericTaskId)
                            .orderByDesc(TaskExecutionDO::getId)
                            .last("LIMIT 1"));
        }
        
        if (executionStatusDO == null) {
            log.warn("Task execution status not found for taskId: {}, executionId: {}", taskId, executionId);
            return null;
        }
        
        // 获取规则执行详情
        List<RuleExecutionDO> ruleExecutionStatusList = ruleExecutionRepository.list(
                Wrappers.lambdaQuery(RuleExecutionDO.class)
                        .eq(RuleExecutionDO::getTaskExecutionId, executionStatusDO.getId()));
        
        // 构建任务执行报告
        InspectionTaskReport report = new InspectionTaskReport();
        report.setTaskId(taskId);
        report.setTaskName(executionStatusDO.getTaskName());
        report.setExecutionId(executionStatusDO.getId());
        report.setTriggerMode(executionStatusDO.getTriggerMode());
        report.setExecutionStatus(executionStatusDO.getExecutionStatus());
        report.setExecutionResult(executionStatusDO.getExecutionResult());
        report.setScheduledTime(executionStatusDO.getScheduledTime());
        report.setExecutionTime(executionStatusDO.getExecutionTime());
        report.setStartTime(executionStatusDO.getStartTime());
        report.setEndTime(executionStatusDO.getEndTime());
        report.setDuration(executionStatusDO.getDuration());
        report.setRuleTotal(executionStatusDO.getRuleTotal());
        report.setRuleSuccessCount(executionStatusDO.getRuleSuccessCount());
        report.setRuleFailCount(executionStatusDO.getRuleFailCount());
        report.setCoverageRate(executionStatusDO.getCoverageRate());
        report.setSuccessRate(executionStatusDO.getSuccessRate());
        report.setResultSummary(executionStatusDO.getResultSummary());
        
        // 构建规则执行详情列表
        List<InspectionTaskReport.RuleExecutionDetail> ruleExecutionDetails = ruleExecutionStatusList.stream()
                .map(ruleDO -> {
                    InspectionTaskReport.RuleExecutionDetail detail = new InspectionTaskReport.RuleExecutionDetail();
                    detail.setRuleId(ruleDO.getRuleId());
                    detail.setPluginId(ruleDO.getPluginId());
                    detail.setPluginName(ruleDO.getPluginName());
                    detail.setExecutionStatus(ruleDO.getExecutionStatus());
                    detail.setExecutionTime(ruleDO.getExecutionTime());
                    detail.setExecutionResult(ruleDO.getExecutionResult());
                    detail.setExecutionDuration(ruleDO.getExecutionDuration());
                    detail.setResourceId(ruleDO.getResourceId());
                    detail.setResourceName(ruleDO.getResourceName());
                    return detail;
                })
                .collect(Collectors.toList());
        
        report.setRuleExecutionDetails(ruleExecutionDetails);
        
        return report;
    }
    
    @Override
    public ResourceInspectionReport getResourceInspectionReport(String resourceId, LocalDate startDate, LocalDate endDate) {
        // 实现资源巡检报告生成逻辑
        // 这里需要根据资源ID和日期范围查询相关的规则执行记录
        // 然后构建资源巡检报告
        
        // 示例实现，实际需要根据数据库结构进行查询
        ResourceInspectionReport report = new ResourceInspectionReport();
        report.setResourceId(resourceId);
        report.setStartDate(startDate);
        report.setEndDate(endDate);
        
        // TODO: 实现完整的资源巡检报告生成逻辑
        
        return report;
    }
    
    @Override
    public DailyInspectionReport getDailyInspectionReport(LocalDate date) {
        // 实现按日巡检报告生成逻辑
        // 这里需要查询指定日期的所有任务执行记录
        // 然后构建按日巡检报告
        
        LocalDateTime startOfDay = date.atStartOfDay();
        LocalDateTime endOfDay = date.atTime(LocalTime.MAX);
        
        // 查询当天的所有任务执行记录
        List<TaskExecutionDO> executionStatusList = taskExecutionRepository.list(
                Wrappers.lambdaQuery(TaskExecutionDO.class)
                        .between(TaskExecutionDO::getExecutionTime, startOfDay, endOfDay));
        
        // 构建按日巡检报告
        DailyInspectionReport report = new DailyInspectionReport();
        report.setReportDate(date);
        report.setTotalExecutions(executionStatusList.size());
        
        // 统计成功和失败的执行数
        int successCount = 0;
        int failCount = 0;
        int totalRuleExecutions = 0;
        int successRuleExecutions = 0;
        int failedRuleExecutions = 0;
        
        for (TaskExecutionDO execution : executionStatusList) {
            if ("pass".equals(execution.getExecutionResult())) {
                successCount++;
            } else {
                failCount++;
            }
            
            totalRuleExecutions += execution.getRuleTotal();
            successRuleExecutions += execution.getRuleSuccessCount();
            failedRuleExecutions += execution.getRuleFailCount();
        }
        
        report.setSuccessExecutions(successCount);
        report.setFailedExecutions(failCount);
        report.setTotalRuleExecutions(totalRuleExecutions);
        report.setSuccessRuleExecutions(successRuleExecutions);
        report.setFailedRuleExecutions(failedRuleExecutions);
        
        // 计算平均通过率和覆盖率
        if (!executionStatusList.isEmpty()) {
            double avgSuccessRate = executionStatusList.stream()
                    .mapToInt(TaskExecutionDO::getSuccessRate)
                    .filter(rate -> rate >= 0)
                    .average()
                    .orElse(0);
            
            double avgCoverageRate = executionStatusList.stream()
                    .mapToInt(TaskExecutionDO::getCoverageRate)
                    .filter(rate -> rate >= 0)
                    .average()
                    .orElse(0);
            
            report.setAverageSuccessRate(avgSuccessRate);
            report.setAverageCoverageRate(avgCoverageRate);
        }
        
        // 构建任务执行摘要列表
        List<DailyInspectionReport.TaskExecutionSummary> taskExecutions = executionStatusList.stream()
                .map(execution -> {
                    DailyInspectionReport.TaskExecutionSummary summary = new DailyInspectionReport.TaskExecutionSummary();
                    summary.setTaskId(String.format("TASK-%06d", execution.getTaskId()));
                    summary.setTaskName(execution.getTaskName());
                    summary.setExecutionId(execution.getId());
                    summary.setExecutionStatus(execution.getExecutionStatus());
                    summary.setExecutionResult(execution.getExecutionResult());
                    summary.setSuccessRate(execution.getSuccessRate());
                    summary.setResultSummary(execution.getResultSummary());
                    return summary;
                })
                .collect(Collectors.toList());
        
        report.setTaskExecutions(taskExecutions);
        
        // TODO: 实现资源健康状况统计
        
        return report;
    }
    
    @Override
    public IPage<Map<String, Object>> getInspectionReportSummary(LocalDate startDate, LocalDate endDate, IPage<?> page) {
        // 实现巡检报告摘要查询逻辑
        // TODO: 实现完整的报告摘要查询逻辑
        return null;
    }
    
    @Override
    public String exportTaskReport(String taskId, Long executionId, String format) {
        // 实现任务报告导出逻辑
        // TODO: 实现完整的报告导出逻辑
        return null;
    }
    
    @Override
    public String exportDailyReport(LocalDate date, String format) {
        // 实现按日报告导出逻辑
        // TODO: 实现完整的报告导出逻辑
        return null;
    }
}
