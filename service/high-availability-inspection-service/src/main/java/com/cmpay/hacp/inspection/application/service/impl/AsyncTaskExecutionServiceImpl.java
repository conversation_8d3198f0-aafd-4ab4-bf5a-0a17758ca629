package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cmpay.hacp.inspection.application.executor.PluginExecutor;
import com.cmpay.hacp.inspection.application.executor.TargetHost;
import com.cmpay.hacp.inspection.application.service.AsyncTaskExecutionService;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.cmpay.hacp.inspection.domain.model.plugin.InspectionResult;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PluginDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.RulePluginMappingDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskRuleDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskRuleResourceDO;
import com.cmpay.hacp.inspection.infrastructure.repository.PluginRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.RulePluginMappingRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.TaskRuleResourceRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.plugin.core.PluginRegistry;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 异步任务执行服务
 * 负责异步执行巡检任务
 */
@Slf4j
@Service
public class AsyncTaskExecutionServiceImpl implements AsyncTaskExecutionService {
    private final PluginRepository pluginRepository;
    private final TaskRuleResourceRepository taskRuleResourceRepository;
    private final RulePluginMappingRepository rulePluginMappingRepository;
    private final PluginRegistry<PluginExecutor, PluginType> pluginRegistry;
    private final InspectionTaskMonitoringServiceImpl monitoringService;
    private final Executor executor;

    public AsyncTaskExecutionServiceImpl(PluginRepository pluginRepository,
                                         TaskRuleResourceRepository taskRuleResourceRepository,
                                         RulePluginMappingRepository rulePluginMappingRepository,
                                         PluginRegistry<PluginExecutor, PluginType> pluginRegistry,
                                         InspectionTaskMonitoringServiceImpl monitoringService,
                                         @Qualifier("asyncInspectionHostExecutor") Executor executor) {
        this.pluginRepository = pluginRepository;
        this.taskRuleResourceRepository = taskRuleResourceRepository;
        this.rulePluginMappingRepository = rulePluginMappingRepository;
        this.pluginRegistry = pluginRegistry;
        this.monitoringService = monitoringService;
        this.executor = executor;
    }

    /**
     * 异步执行任务
     *
     * @param taskId         任务ID
     * @param taskRuleDOList 规则列表
     */
    @Async("asyncInspectionTaskExecutor")
    @Override
    public void executeTaskAsync(String taskId, List<TaskRuleDO> taskRuleDOList) {
        log.info("Executing task asynchronously: {}", taskId);

        // 规则维度的成功失败统计
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);
        AtomicBoolean hasError = new AtomicBoolean(false);

        // 使用CompletableFuture来并行执行规则
        CompletableFuture.allOf(taskRuleDOList.stream()
                .map(taskRuleDO -> CompletableFuture.runAsync(() -> {
                    try {
                        executeRule(taskId, taskRuleDO, successCount, failCount);
                    } catch (Exception e) {
                        log.error("Error executing rule: {}", taskRuleDO.getRuleId(), e);
                        hasError.set(true);
                        failCount.incrementAndGet();
                    }
                })).toArray(CompletableFuture[]::new)).join();

        // 更新任务状态为已完成
        boolean success = !hasError.get() && failCount.get() == 0;
        monitoringService.markTaskAsCompleted(taskId, success);

        log.info("Task execution completed: {}, success: {}, successCount: {}, failCount: {}",
                taskId, success, successCount.get(), failCount.get());
    }

    /**
     * 执行单个规则
     *
     * @param taskId       任务ID
     * @param taskRuleDO   规则DO
     * @param successCount 成功计数器
     * @param failCount    失败计数器
     */
    private void executeRule(String taskId, TaskRuleDO taskRuleDO, AtomicInteger successCount, AtomicInteger failCount) {
        String ruleId = taskRuleDO.getRuleId();
        log.info("Executing rule: {} for task: {}", ruleId, taskId);

        // 获取规则关联的资源列表
        List<TaskRuleResourceDO> resourceList = taskRuleResourceRepository.list(
                Wrappers.lambdaQuery(TaskRuleResourceDO.class)
                        .eq(TaskRuleResourceDO::getTaskId, taskId)
                        .eq(TaskRuleResourceDO::getRuleId, ruleId));

        if (resourceList.isEmpty()) {
            log.warn("No resources found for rule: {}", ruleId);
            return;
        }

        // 获取规则关联的插件
        RulePluginMappingDO rulePluginMappingDO = rulePluginMappingRepository.getOne(
                Wrappers.lambdaQuery(RulePluginMappingDO.class)
                        .eq(RulePluginMappingDO::getRuleId, ruleId));

        if (rulePluginMappingDO == null) {
            log.warn("No plugin mapping found for rule: {}", ruleId);
            return;
        }

        String pluginId = rulePluginMappingDO.getPluginId();
        PluginDO pluginDO = pluginRepository.getOne(
                Wrappers.lambdaQuery(PluginDO.class)
                        .eq(PluginDO::getPluginId, pluginId));

        if (pluginDO == null) {
            log.warn("Plugin not found: {}", pluginId);
            return;
        }

        PluginType type = pluginDO.getType();

        // 获取对应的插件实现
        PluginExecutor pluginExecutor = pluginRegistry.getPluginFor(type)
                .orElseThrow(() -> new RuntimeException("No plugin found for type: " + type));

        // 主机维度的成功失败统计
        AtomicInteger hostSuccessCount = new AtomicInteger(0);
        AtomicInteger hostFailCount = new AtomicInteger(0);

        try {
            // 为每个资源（主机）创建CompletableFuture任务
            List<CompletableFuture<InspectionResult>> futures = resourceList.stream()
                    .map(resource -> CompletableFuture.supplyAsync(() -> {
                        return executePluginForHost(taskId, ruleId, pluginId, resource,
                                pluginExecutor, pluginDO.getName());
                    }, executor)) // 使用自定义线程池
                    .collect(Collectors.toList());

            // 等待所有主机执行完成并收集结果
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            // 统计主机维度和规则维度的结果
            boolean ruleSuccess = true;
            for (CompletableFuture<InspectionResult> future : futures) {
                try {
                    InspectionResult result = future.get();
                    if (result.isSuccess()) {
                        hostSuccessCount.incrementAndGet();
                    } else {
                        hostFailCount.incrementAndGet();
                        ruleSuccess = false;
                    }
                } catch (Exception e) {
                    log.error("Error getting inspection result", e);
                    hostFailCount.incrementAndGet();
                    ruleSuccess = false;
                }
            }

            // 规则维度统计：只有当所有主机都成功时，规则才算成功
            if (ruleSuccess) {
                successCount.incrementAndGet();
            } else {
                failCount.incrementAndGet();
            }

            log.info("Rule execution completed: {}, ruleSuccess: {}, hostSuccessCount: {}, hostFailCount: {}",
                    ruleId, ruleSuccess, hostSuccessCount.get(), hostFailCount.get());

        } catch (Exception e) {
            log.error("Error executing rule: {} for task: {}", ruleId, taskId, e);
            failCount.incrementAndGet();
        }
    }

    private InspectionResult executePluginForHost(String taskId, String ruleId, String pluginId,
                                                  TaskRuleResourceDO resource, PluginExecutor pluginExecutor,
                                                  String pluginName) {
        try {
            // 将TaskRuleResourceDO转换为TargetHost
            TargetHost targetHost = convertToTargetHost(resource);

            log.info("Executing plugin: {} for host: {} in rule: {}", pluginId, targetHost.getHost(), ruleId);

            // 执行巡检
            InspectionResult result = pluginExecutor.execute(ruleId, pluginId, targetHost);

            // 按单一主机记录执行结果，包含资源信息
            monitoringService.recordRuleExecutionResult(taskId, ruleId, pluginId, result,
                    resource.getResourceId(), getResourceName(resource));

            log.info("Plugin execution completed for host: {}, resource: {}, success: {}",
                    targetHost.getHost(), resource.getResourceId(), result.isSuccess());

            return result;

        } catch (Exception e) {
            log.error("Error executing plugin: {} for resource: {}", pluginId, resource.getResourceId(), e);

            // 记录失败结果
            InspectionResult failResult = InspectionResult.builder()
                    .success(false)
                    .message("Execution error")
                    .details(e.getMessage())
                    .pluginName(pluginName)
                    .build();

            monitoringService.recordRuleExecutionResult(taskId, ruleId, pluginId, failResult,
                    resource.getResourceId(), getResourceName(resource));

            return failResult;
        }
    }

    private TargetHost convertToTargetHost(TaskRuleResourceDO resource) {
        // TODO: 根据实际的TaskRuleResourceDO结构实现转换逻辑
        return TargetHost.builder().build();
    }

    /**
     * 获取资源名称
     * @param resource 资源对象
     * @return 资源名称
     */
    private String getResourceName(TaskRuleResourceDO resource) {
        // 这里可以根据实际需求从资源ID获取资源名称
        // 可能需要查询资源管理服务或缓存
        // 暂时返回资源ID作为名称
        return resource.getResourceId();
    }
}
