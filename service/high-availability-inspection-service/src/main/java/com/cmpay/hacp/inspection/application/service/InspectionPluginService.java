package com.cmpay.hacp.inspection.application.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cmpay.hacp.inspection.domain.model.plugin.InspectionPlugin;

/**
 * 巡检插件服务接口
 */
public interface InspectionPluginService {

    /**
     * 创建巡检插件
     *
     * @param inspectionPlugin 插件信息
     * @return 创建的插件ID
     */
    String createPlugin(InspectionPlugin inspectionPlugin);

    /**
     * 更新巡检插件
     *
     * @param inspectionPlugin 插件信息
     * @return 是否更新成功
     */
    boolean updatePlugin(InspectionPlugin inspectionPlugin);

    /**
     * 删除巡检插件
     *
     * @param pluginId 插件ID
     * @return 是否删除成功
     */
    void deletePlugin(String pluginId);

    /**
     * 获取巡检插件详情
     *
     * @param pluginId 插件ID
     * @return 插件详情
     */
    InspectionPlugin getPluginDetail(String pluginId);


    /**
     * 分页查询巡检插件列表
     *
     * @param page 分页对象
     * @param inspectionPlugin 查询条件
     * @return 分页结果
     */
    IPage<InspectionPlugin> getPluginPage(IPage<?> page, InspectionPlugin inspectionPlugin);

}
