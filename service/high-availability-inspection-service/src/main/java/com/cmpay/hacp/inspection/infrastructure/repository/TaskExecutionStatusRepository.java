package com.cmpay.hacp.inspection.infrastructure.repository;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.InspectionTaskExecutionDO;
import com.cmpay.hacp.inspection.infrastructure.mapper.TaskExecutionStatusMapper;
import org.springframework.stereotype.Repository;

/**
 * 任务执行状态Repository
 */
@Repository
public class TaskExecutionStatusRepository extends CrudRepository<TaskExecutionStatusMapper, InspectionTaskExecutionDO> {
}
