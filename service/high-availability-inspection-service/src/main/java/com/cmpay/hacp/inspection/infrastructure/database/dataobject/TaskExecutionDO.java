package com.cmpay.hacp.inspection.infrastructure.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cmpay.hacp.inspection.domain.model.enums.ExecutionResult;
import com.cmpay.hacp.inspection.domain.model.enums.ExecutionStatus;
import com.cmpay.hacp.inspection.domain.model.enums.TriggerMode;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 巡检任务执行记录实体类
 * 记录单次巡检任务的整体执行状态和结果汇总
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inspection_task_execution")
public class TaskExecutionDO extends BaseDO {
    /**
     * 任务ID，外键，关联InspectionTask表的id
     */
    private String taskId;

    /**
     * 任务名称，冗余存储，便于查询显示
     */
    private String taskName;

    /**
     * 触发方式 0:定时触发 1:手动触发
     */
    private TriggerMode triggerMode;

    /**
     * 执行状态 0:待执行 1:执行中 2:已完成 3:失败 4:已停止
     */
    private ExecutionStatus executionStatus;

    /**
     * 执行结果 pass:通过 fail:失败
     */
    private ExecutionResult executionResult;

    /**
     * 计划执行时间，定时触发时的计划时间
     */
    private LocalDateTime scheduledTime;

    /**
     * 实际执行时间，任务开始执行的时间
     */
    private LocalDateTime executionTime;

    /**
     * 开始时间，实际开始执行的时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间，执行完成的时间
     */
    private LocalDateTime endTime;

    /**
     * 耗时(秒)，执行总耗时
     */
    private Integer duration;

    /**
     * 关联规则总数，本次执行涉及的规则总数
     */
    private Integer ruleTotal;

    /**
     * 执行成功规则数
     */
    private Integer ruleSuccessCount;

    /**
     * 执行失败规则数
     */
    private Integer ruleFailCount;

    /**
     * 资源覆盖率(%)
     */
    private Integer coverageRate;

    /**
     * 规则通过率(%)，成功规则数/总规则数
     */
    private Integer successRate;

    /**
     * 执行结果概要
     */
    private String resultSummary;

    /**
     * 状态概要
     */
    private String statusSummary;
}
