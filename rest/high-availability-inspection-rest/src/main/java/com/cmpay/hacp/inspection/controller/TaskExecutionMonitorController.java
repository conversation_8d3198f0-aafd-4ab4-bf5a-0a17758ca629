package com.cmpay.hacp.inspection.controller;

import com.cmpay.hacp.inspection.application.service.InspectionReportService;
import com.cmpay.hacp.inspection.application.service.impl.InspectionTaskMonitoringServiceImpl;
import com.cmpay.hacp.inspection.domain.model.report.InspectionTaskReport;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskExecutionDO;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 任务执行监控控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/inspection/monitor")
@Api(tags = "任务执行监控接口")
public class TaskExecutionMonitorController {
    private final InspectionTaskMonitoringServiceImpl monitoringService;
    private final InspectionReportService reportService;
    
    /**
     * 获取任务执行状态
     * @param taskId 任务ID
     * @return 任务执行状态
     */
    @ApiOperation(value = "获取任务执行状态", notes = "根据任务ID获取最新的执行状态")
    @ApiResponse(code = 200, message = "成功")
    @GetMapping("/status/{taskId}")
    @PreAuthorize("hasPermission('TaskExecutionMonitorController','inspection:task:monitor')")
    public DefaultRspDTO<Map<String, Object>> getTaskExecutionStatus(
            @ApiParam(name = "taskId", value = "任务ID", example = "TASK-000001", required = true)
            @PathVariable("taskId") String taskId) {
        
        TaskExecutionDO status = monitoringService.getTaskExecutionStatus(taskId);
        
        if (status == null) {
            return DefaultRspDTO.newInstance("HAI10001", "任务执行状态不存在");
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("taskId", taskId);
        result.put("executionId", status.getId());
        result.put("status", status.getExecutionStatus());
        result.put("result", status.getExecutionResult());
        result.put("startTime", status.getStartTime());
        result.put("endTime", status.getEndTime());
        result.put("duration", status.getDuration());
        result.put("ruleTotal", status.getRuleTotal());
        result.put("ruleSuccessCount", status.getRuleSuccessCount());
        result.put("ruleFailCount", status.getRuleFailCount());
        result.put("successRate", status.getSuccessRate());
        
        return DefaultRspDTO.newSuccessInstance(result);
    }
    
    /**
     * 获取任务执行报告
     * @param taskId 任务ID
     * @param executionId 执行ID
     * @return 任务执行报告
     */
    @ApiOperation(value = "获取任务执行报告", notes = "根据任务ID和执行ID获取详细的执行报告")
    @ApiResponse(code = 200, message = "成功")
    @GetMapping("/report/{taskId}")
    @PreAuthorize("hasPermission('TaskExecutionMonitorController','inspection:task:report')")
    public DefaultRspDTO<InspectionTaskReport> getTaskExecutionReport(
            @ApiParam(name = "taskId", value = "任务ID", example = "TASK-000001", required = true)
            @PathVariable("taskId") String taskId,
            
            @ApiParam(name = "executionId", value = "执行ID", example = "1")
            @RequestParam(value = "executionId", required = false) Long executionId) {
        
        InspectionTaskReport report = reportService.getTaskExecutionReport(taskId, executionId);
        
        if (report == null) {
            return DefaultRspDTO.newInstance("HAI10002", "任务执行报告不存在");
        }
        
        return DefaultRspDTO.newSuccessInstance(report);
    }
}
